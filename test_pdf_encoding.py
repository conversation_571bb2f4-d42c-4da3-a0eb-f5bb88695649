#!/usr/bin/env python3
"""
PDF繁体中文编码问题诊断测试
用于检测PDF读取、文本切片、向量化存储过程中的编码问题
"""

import os
import sys
import logging
import traceback
from typing import List, Dict, Any
import unittest

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PDFEncodingTest(unittest.TestCase):
    """PDF编码问题测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.config = Config()
        self.reports_dir = "./reports"
        
        # 检查测试文件是否存在
        if not os.path.exists(self.reports_dir):
            self.skipTest(f"测试目录 {self.reports_dir} 不存在")
            
        # 查找PDF文件
        self.pdf_files = []
        for file in os.listdir(self.reports_dir):
            if file.endswith('.pdf'):
                self.pdf_files.append(os.path.join(self.reports_dir, file))
        
        if not self.pdf_files:
            self.skipTest("没有找到PDF文件进行测试")
            
        logger.info(f"找到 {len(self.pdf_files)} 个PDF文件: {self.pdf_files}")

    def test_pdf_reading_with_llamaindex(self):
        """测试使用LlamaIndex读取PDF文件"""
        print("\n=== 测试LlamaIndex PDF读取 ===")
        
        try:
            from llama_index.core import SimpleDirectoryReader
            
            reader = SimpleDirectoryReader(
                input_dir=self.reports_dir,
                recursive=True,
                filename_as_id=True,
                required_exts=[".pdf"]
            )
            
            documents = reader.load_data()
            logger.info(f"成功读取 {len(documents)} 个文档")
            
            for i, doc in enumerate(documents):
                print(f"\n--- 文档 {i+1}: {doc.doc_id} ---")
                
                if doc.text:
                    # 检查文本长度
                    text_length = len(doc.text)
                    print(f"文本长度: {text_length} 字符")
                    
                    # 显示文本前200个字符
                    text_preview = doc.text[:200]
                    print(f"文本预览: {text_preview}")
                    
                    # 检查是否包含中文字符
                    chinese_chars = [char for char in text_preview if '\u4e00' <= char <= '\u9fff']
                    print(f"中文字符数量: {len(chinese_chars)}")
                    if chinese_chars:
                        print(f"中文字符示例: {''.join(chinese_chars[:10])}")
                    
                    # 检查编码
                    try:
                        encoded = text_preview.encode('utf-8')
                        decoded = encoded.decode('utf-8')
                        print("✓ UTF-8编码检查通过")
                    except (UnicodeEncodeError, UnicodeDecodeError) as e:
                        print(f"✗ UTF-8编码错误: {e}")
                        
                    # 检查是否有乱码特征
                    garbled_indicators = ['�', '?', '\ufffd']
                    has_garbled = any(indicator in text_preview for indicator in garbled_indicators)
                    if has_garbled:
                        print("⚠️  检测到可能的乱码字符")
                    else:
                        print("✓ 未检测到明显乱码")
                        
                else:
                    print("⚠️  文档没有文本内容")
                    
        except Exception as e:
            logger.error(f"LlamaIndex PDF读取测试失败: {e}")
            traceback.print_exc()
            self.fail(f"LlamaIndex PDF读取失败: {e}")

    def test_pdf_reading_with_pypdf(self):
        """测试使用PyPDF直接读取PDF文件"""
        print("\n=== 测试PyPDF直接读取 ===")
        
        try:
            import pypdf
            
            for pdf_file in self.pdf_files[:2]:  # 只测试前2个文件
                print(f"\n--- 测试文件: {pdf_file} ---")
                
                with open(pdf_file, 'rb') as file:
                    pdf_reader = pypdf.PdfReader(file)
                    num_pages = len(pdf_reader.pages)
                    print(f"PDF页数: {num_pages}")
                    
                    # 读取前几页文本
                    all_text = ""
                    for page_num in range(min(3, num_pages)):  # 只读前3页
                        page = pdf_reader.pages[page_num]
                        page_text = page.extract_text()
                        all_text += page_text
                        
                        if page_text:
                            print(f"第{page_num+1}页文本长度: {len(page_text)}")
                            preview = page_text[:100]
                            print(f"第{page_num+1}页预览: {preview}")
                        else:
                            print(f"第{page_num+1}页没有文本")
                    
                    if all_text:
                        # 检查整体文本
                        print(f"总文本长度: {len(all_text)}")
                        
                        # 检查中文字符
                        chinese_chars = [char for char in all_text[:500] if '\u4e00' <= char <= '\u9fff']
                        print(f"中文字符数量: {len(chinese_chars)}")
                        
                        # 检查编码
                        try:
                            encoded = all_text[:200].encode('utf-8')
                            decoded = encoded.decode('utf-8')
                            print("✓ UTF-8编码检查通过")
                        except (UnicodeEncodeError, UnicodeDecodeError) as e:
                            print(f"✗ UTF-8编码错误: {e}")
                            
        except ImportError:
            self.skipTest("PyPDF库未安装")
        except Exception as e:
            logger.error(f"PyPDF读取测试失败: {e}")
            traceback.print_exc()

    def test_text_chunking(self):
        """测试文本切片功能"""
        print("\n=== 测试文本切片 ===")
        
        try:
            from llama_index.core import SimpleDirectoryReader
            from llama_index.core.node_parser import SentenceSplitter
            
            # 读取文档
            reader = SimpleDirectoryReader(
                input_dir=self.reports_dir,
                recursive=True,
                filename_as_id=True,
                required_exts=[".pdf"]
            )
            
            documents = reader.load_data()
            if not documents:
                self.skipTest("没有读取到文档")
                
            # 创建文本切片器
            splitter = SentenceSplitter(
                chunk_size=self.config.CHUNK_SIZE,
                chunk_overlap=self.config.CHUNK_OVERLAP
            )
            
            # 对第一个文档进行切片
            doc = documents[0]
            print(f"原始文档长度: {len(doc.text) if doc.text else 0}")
            
            if doc.text:
                # 显示原始文本预览
                original_preview = doc.text[:200]
                print(f"原始文本预览: {original_preview}")
                
                # 进行切片
                nodes = splitter.get_nodes_from_documents([doc])
                print(f"切片后节点数量: {len(nodes)}")
                
                # 检查前几个节点
                for i, node in enumerate(nodes[:3]):
                    if hasattr(node, 'text') and node.text:
                        node_text = node.text
                        print(f"\n节点 {i+1} 长度: {len(node_text)}")
                        print(f"节点 {i+1} 预览: {node_text[:100]}")
                        
                        # 检查编码
                        try:
                            encoded = node_text[:100].encode('utf-8')
                            decoded = encoded.decode('utf-8')
                            print(f"节点 {i+1} ✓ UTF-8编码检查通过")
                        except (UnicodeEncodeError, UnicodeDecodeError) as e:
                            print(f"节点 {i+1} ✗ UTF-8编码错误: {e}")
                            
                        # 检查中文字符
                        chinese_chars = [char for char in node_text[:100] if '\u4e00' <= char <= '\u9fff']
                        if chinese_chars:
                            print(f"节点 {i+1} 中文字符示例: {''.join(chinese_chars[:5])}")
                    elif hasattr(node, 'get_content'):
                        try:
                            content = node.get_content()
                            print(f"节点 {i+1} 内容长度: {len(content) if content else 0}")
                            if content:
                                print(f"节点 {i+1} 内容预览: {content[:100]}")
                        except Exception as e:
                            print(f"节点 {i+1} 获取内容失败: {e}")
                    else:
                        print(f"节点 {i+1} 无法访问文本内容")
                        
        except Exception as e:
            logger.error(f"文本切片测试失败: {e}")
            traceback.print_exc()
            self.fail(f"文本切片测试失败: {e}")

    def test_encoding_detection(self):
        """测试编码检测和修复"""
        print("\n=== 测试编码检测和修复 ===")
        
        # 测试常见的编码问题
        test_texts = [
            "正常的繁体中文：財務報告、營業收入、淨利潤",
            "简体中文：财务报告、营业收入、净利润", 
            "混合文本：Company 財務報告 2024年",
            "特殊字符：361°、AT&T、C&A"
        ]
        
        for i, text in enumerate(test_texts):
            print(f"\n测试文本 {i+1}: {text}")
            
            # UTF-8编码测试
            try:
                utf8_encoded = text.encode('utf-8')
                utf8_decoded = utf8_encoded.decode('utf-8')
                print(f"✓ UTF-8编码/解码成功")
                assert text == utf8_decoded, "UTF-8编码解码不一致"
            except Exception as e:
                print(f"✗ UTF-8编码/解码失败: {e}")
                
            # 检查中文字符
            chinese_chars = [char for char in text if '\u4e00' <= char <= '\u9fff']
            print(f"中文字符数量: {len(chinese_chars)}")
            
            # 检查特殊字符
            special_chars = [char for char in text if ord(char) > 127 and not ('\u4e00' <= char <= '\u9fff')]
            if special_chars:
                print(f"特殊字符: {''.join(set(special_chars))}")

def run_diagnostic_tests():
    """运行诊断测试"""
    print("开始PDF繁体中文编码诊断测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(PDFEncodingTest('test_pdf_reading_with_llamaindex'))
    suite.addTest(PDFEncodingTest('test_pdf_reading_with_pypdf'))
    suite.addTest(PDFEncodingTest('test_text_chunking'))
    suite.addTest(PDFEncodingTest('test_encoding_detection'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result

if __name__ == "__main__":
    try:
        result = run_diagnostic_tests()
        
        if result.wasSuccessful():
            print("\n✓ 所有测试通过")
        else:
            print(f"\n✗ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
            
    except Exception as e:
        print(f"测试运行失败: {e}")
        traceback.print_exc()
