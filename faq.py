# financial_qa_generator_llamaindex.py
# 基于LlamaIndex生成高质量问答对，用于评估智能体问答准确率
import traceback
import re

import json
import logging
from llama_index.core.llms import LLM
from llama_index.core.embeddings import BaseEmbedding
from llama_index.core.callbacks import CallbackManager
from llama_index.core.base.llms.types import (
    CompletionResponse,
    CompletionResponseGen,
    ChatResponse,
    ChatResponseGen,
    LLMMetadata,
    ChatMessage,
    MessageRole,
)
from llama_index.core.llms.callbacks import llm_completion_callback, llm_chat_callback
from typing import Optional, List, Any, Sequence
from typing import List, Dict, Any
from datetime import datetime
import pandas as pd

# Gemini SDK
import google.generativeai as genai

# OpenRouter for LLM
import openai

from dotenv import load_dotenv
# load env
load_dotenv()

# 导入最新版本的LlamaIndex
try:
    print("尝试导入最新版的LlamaIndex...")
    # 使用最新版本的导入方式
    from llama_index.core import (
        SimpleDirectoryReader,
        VectorStoreIndex,
        StorageContext,
        Document,
        Settings
    )
    from llama_index.core.node_parser import SentenceSplitter
    from llama_index.core.embeddings import BaseEmbedding
    from llama_index.vector_stores.milvus import MilvusVectorStore
    from llama_index.core.evaluation import generate_question_context_pairs
    
    print("成功导入最新版的LlamaIndex")
    
    # 标记使用的是新版本
    IS_NEW_VERSION = True
except ImportError as e:
    print(f"导入最新版的LlamaIndex失败: {e}")
    print("请确保已正确安装最新版的LlamaIndex及其依赖包")
    raise ImportError("无法导入LlamaIndex，请检查安装")



# Milvus SaaS 集成

# 配置文件
from config import Config

# PDF文本提取器
from pdf_text_extractor import PDFTextExtractor

# 配置日志
config = Config()
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)


class GeminiEmbedding(BaseEmbedding):
    """Gemini Embedding 适配器，兼容LlamaIndex"""
    
    model_name: str

    def __init__(
        self, 
        api_key: str, 
        model: str = "models/text-embedding-004",
        embed_batch_size: int = 10, # Default from LlamaIndex, can be configured via config.py if needed
        callback_manager: Optional[CallbackManager] = None
    ):
        super().__init__(
            embed_batch_size=embed_batch_size, 
            callback_manager=callback_manager,
            model_name=model # Pass model_name to BaseEmbedding
        )
        genai.configure(api_key=api_key)
        self.model_name = model # Keep for direct use, superclass also stores it

    def _get_query_embedding(self, query: str) -> List[float]:
        """获取查询embedding"""
        if not config.get_token_limit_for_text(query): # type: ignore
            query = config.truncate_text_to_token_limit(query) # type: ignore
            logger.warning(f"文本被截断以适应token限制: {len(query)} 字符")
        
        try:
            result = genai.embed_content( # type: ignore
                model=self.model_name,
                content=query,
                task_type="retrieval_query"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Gemini embedding 失败: {e}")
            return [0.0] * config.EMBEDDING_DIMENSION # type: ignore
    
    async def _aget_query_embedding(self, query: str) -> List[float]:
        """异步获取查询embedding"""
        # This is still synchronous as per original code. 
        # For true async, an async http client for genai would be needed.
        return self._get_query_embedding(query)

    def _get_text_embedding(self, text: str) -> List[float]:
        """获取文本embedding"""
        if not config.get_token_limit_for_text(text): # type: ignore
            text = config.truncate_text_to_token_limit(text) # type: ignore
            logger.warning(f"文本被截断以适应token限制: {len(text)} 字符")
        
        try:
            result = genai.embed_content( # type: ignore
                model=self.model_name,
                content=text,
                task_type="retrieval_document" # Corrected task_type from 'text' to 'retrieval_document'
            )
            embedding = result['embedding']
            logger.info(f"Gemini Embedding: 成功嵌入文本，开头为: '{text[:80]}...' ({len(embedding)} dims)")
            return embedding
        except Exception as e:
            logger.error(f"Gemini embedding 失败: {e}")
            return [0.0] * config.EMBEDDING_DIMENSION # type: ignore

    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本embedding"""
        all_embeddings: List[List[float]] = []
        # self.embed_batch_size is inherited from BaseEmbedding
        for i in range(0, len(texts), self.embed_batch_size):
            batch_texts = texts[i : i + self.embed_batch_size]
            # The original code iterated and called _get_text_embedding for each.
            # A more optimized version would use a true batch API from genai if available.
            batch_embeddings = [self._get_text_embedding(text) for text in batch_texts]
            all_embeddings.extend(batch_embeddings)
        logger.info(f"Gemini Embedding: 成功嵌入一批 {len(texts)} 个文本。")
        return all_embeddings

    async def _aget_text_embedding(self, text: str) -> List[float]:
        # For true async, an async http client for genai would be needed.
        return self._get_text_embedding(text)

    async def _aget_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        # For true async, an async http client for genai would be needed.
        return self._get_text_embeddings(texts)


class OpenRouterLLM(LLM):
    """OpenRouter LLM 适配器，兼容LlamaIndex"""
    
    model: str
    temperature: float
    max_tokens: int
    context_window: int
    client: Any # openai.OpenAI client

    def __init__(
        self, 
        api_key: str, 
        model: str,  # Parameter for model name
        base_url: str = "https://openrouter.ai/api/v1",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        context_window: int = 8192, # Default, can be configured via config.py if needed
        callback_manager: Optional[CallbackManager] = None,
        **kwargs: Any # Keep for flexibility with LlamaIndex's LLM instantiation
    ):
        # Create the client instance first
        client_instance = openai.OpenAI(api_key=api_key, base_url=base_url) # type: ignore

        # Prepare data for Pydantic validation in super().__init__
        # These keys must match the field names (class attributes) defined in OpenRouterLLM
        init_data = {
            "model": model,  # Use the 'model' parameter value
            "temperature": temperature,
            "max_tokens": max_tokens,
            "context_window": context_window,
            "client": client_instance, # Pass the created client instance
            "callback_manager": callback_manager,
            **kwargs # Pass along any other kwargs that might be expected by LLM or Pydantic
        }
        
        # The Pydantic BaseModel.__init__ (called via LLM's super chain)
        # will use init_data to populate the fields of the OpenRouterLLM instance.
        # Fields like self.model, self.client, etc., will be set by this call.
        super().__init__(**init_data)

    @property
    def metadata(self) -> LLMMetadata:
        return LLMMetadata(
            context_window=self.context_window,
            num_output=self.max_tokens,
            is_chat_model=True, # Assuming OpenRouter models are chat-based
            model_name=self.model,
        )

    @llm_completion_callback()
    def complete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        """生成文本补全"""
        try:
            # Using chat completions for completion is a common pattern
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                **kwargs # Pass additional kwargs from LlamaIndex
            )
            content = response.choices[0].message.content or "无法生成回答"
            return CompletionResponse(text=content, raw=response.model_dump())
        except Exception as e:
            logger.error(f"OpenRouter LLM complete call failed: {e}")
            return CompletionResponse(text=f"抱歉，无法生成回答: {e}", raw={"error": str(e)})

    @llm_chat_callback()
    def chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        """生成聊天补全"""
        openai_messages = []
        for msg in messages:
            # Ensure content is not None, which can happen with some tool call messages
            content = msg.content if msg.content is not None else ""
            openai_messages.append({"role": msg.role.value, "content": content})
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages, # type: ignore
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                **kwargs # Pass additional kwargs from LlamaIndex
            )
            response_content = response.choices[0].message.content or ""
            # response_role_str = response.choices[0].message.role
            # response_role = MessageRole(response_role_str) if response_role_str else MessageRole.ASSISTANT

            return ChatResponse(
                message=ChatMessage(
                    role=MessageRole.ASSISTANT, # OpenAI typically responds as assistant
                    content=response_content
                ),
                raw=response.model_dump()
            )
        except Exception as e:
            logger.error(f"OpenRouter LLM chat call failed: {e}")
            return ChatResponse(
                message=ChatMessage(
                    role=MessageRole.ASSISTANT,
                    content=f"抱歉，无法生成聊天回答: {e}"
                ),
                raw={"error": str(e)}
            )

    # --- Stream methods (basic implementation, not true streaming) ---
    @llm_completion_callback()
    def stream_complete(self, prompt: str, **kwargs: Any) -> CompletionResponseGen:
        # This is a non-streaming stub. For true streaming, you'd use the `stream=True` 
        # option with the OpenAI client and yield deltas.
        response = self.complete(prompt, **kwargs)
        def gen() -> CompletionResponseGen:
            yield response 
        return gen()

    @llm_chat_callback()
    def stream_chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponseGen:
        # This is a non-streaming stub.
        response = self.chat(messages, **kwargs)
        def gen() -> ChatResponseGen:
            yield response
        return gen()

    # --- Async methods (basic implementation, not true async) ---
    @llm_completion_callback()
    async def acomplete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        # This should ideally use an async HTTP client (e.g., httpx with openai.AsyncOpenAI)
        return self.complete(prompt, **kwargs) 

    @llm_chat_callback()
    async def achat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        # This should ideally use an async HTTP client
        return self.chat(messages, **kwargs)

    @llm_completion_callback()
    async def astream_complete(self, prompt: str, **kwargs: Any) -> CompletionResponseGen:
        # This should ideally use an async HTTP client and yield
        response = self.complete(prompt, **kwargs) 
        async def gen() -> CompletionResponseGen:
            yield response
        return gen()

    @llm_chat_callback()
    async def astream_chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponseGen:
        # This should ideally use an async HTTP client and yield
        response = self.chat(messages, **kwargs)
        async def gen() -> ChatResponseGen:
            yield response
        return gen()


class FinancialQAGenerator:
    """财报问答生成器 - 基于LlamaIndex，用于生成评估数据集"""
    
    def __init__(self):
        """初始化配置"""
        try:
            print("开始初始化FinancialQAGenerator...")
            # 验证配置
            if not config.validate_config():
                raise ValueError("配置验证失败，请检查环境变量")
            
            print("配置验证通过，设置集合名称...")
            self.collection_name = config.MILVUS_COLLECTION_NAME
            
            print("配置OpenRouter LLM...")
            # 配置 LLM 和 Embedding
            try:
                self.llm = OpenRouterLLM(
                    api_key=config.OPENROUTER_API_KEY,
                    model=config.OPENROUTER_MODEL,
                    base_url=config.OPENROUTER_BASE_URL
                )
                print("OpenRouter LLM配置成功")
            except Exception as e:
                print(f"OpenRouter LLM配置失败: {e}")
                raise
            
            print("配置Gemini Embedding...")
            try:
                self.embed_model = GeminiEmbedding(
                    api_key=config.GEMINI_API_KEY,
                    model=config.GEMINI_EMBEDDING_MODEL
                )
                print("Gemini Embedding配置成功")
            except Exception as e:
                print(f"Gemini Embedding配置失败: {e}")
                raise
            
            print("初始化Milvus向量存储...")
            try:
                # 初始化 Milvus 向量存储
                self.vector_store = MilvusVectorStore(
                    uri=config.MILVUS_BASE_URL,
                    token=config.MILVUS_TOKEN,
                    collection_name=self.collection_name,
                    dim=config.EMBEDDING_DIMENSION,
                    overwrite=False
                )
                print("Milvus向量存储初始化成功")
            except Exception as e:
                print(f"Milvus向量存储初始化失败: {e}")
                raise
            
            print("配置LlamaIndex Settings...")
            try:
                # 配置 LlamaIndex Settings
                node_parser = SentenceSplitter(
                    chunk_size=config.CHUNK_SIZE,
                    chunk_overlap=config.CHUNK_OVERLAP
                )
                
                # 使用最新的LlamaIndex API
                print("使用最新的LlamaIndex API配置...")
                try:
                    # 使用全局Settings
                    Settings.llm = self.llm
                    Settings.embed_model = self.embed_model
                    Settings.node_parser = node_parser
                    print("LlamaIndex Settings配置成功")
                except Exception as e:
                    print(f"LlamaIndex Settings配置失败:")
                    traceback.print_exc()
                    raise
                
                print("LlamaIndex配置完成")
            except Exception as e:
                print(f"LlamaIndex Settings配置失败:")
                traceback.print_exc()
                raise
            
            logger.info(f"初始化完成，使用模型: {config.OPENROUTER_MODEL}")
            logger.info(f"Embedding模型: {config.GEMINI_EMBEDDING_MODEL}")
            logger.info(f"Milvus集合: {self.collection_name}")
            self.metadata_cache: Dict[str, Dict[str, str]] = {} # Cache for filename metadata
            print("FinancialQAGenerator初始化完成")
        except Exception as e:
            print(f"FinancialQAGenerator初始化失败: {e}")
            raise

    def _get_original_filepath(self, doc_part_filepath: str) -> str:
        """从文档部分的文件路径中提取原始文件路径，移除如 '_part_0' 等后缀。"""
        match = re.search(r"_part_\d+$", doc_part_filepath)
        if match:
            return doc_part_filepath[:match.start()]
        return doc_part_filepath

    def _extract_metadata_from_filename_llm(self, filename: str) -> Dict[str, str]:
        """使用LLM从文件名中提取公司名和财报年份"""
        original_filepath = self._get_original_filepath(filename)
        if original_filepath in self.metadata_cache:
            logger.info(f"使用缓存的元数据 for '{original_filepath}' (from part '{filename}')")
            return self.metadata_cache[original_filepath]

        # If not in cache, proceed to LLM call
        prompt = f"""
Given the financial report filename: "{filename}"

Extract the following information:
1. Company Name: The primary name of the company. If there are stock codes or multiple names, choose the most recognizable company name.
2. Financial Year: The main financial year this report covers. For fiscal years (e.g., FY2024, or a period like 2023-2024), use the ending year (e.g., 2024). For reports dated at year-end (e.g., covering up to 2023.12.31), use that year (e.g., 2023). For filenames like 'Company_2023_Annual_Report.pdf', the year is 2023. For 'Company_FY24_Report.pdf', the year is 2024.

Return the information in JSON format like this:
{{"company": "Extracted Company Name", "year": "YYYY"}}

If you cannot confidently extract either piece of information, use "未知公司" for the company or "未知年份" for the year in the JSON response.
Example for '波司登03998 - 202324年年度報告.pdf': {{"company": "波司登", "year": "2024"}}
Example for '耐克2024.05.31.pdf': {{"company": "耐克", "year": "2024"}} (assuming this is the end of their FY2024)
Example for 'lululemon-2025.1.28.pdf': {{"company": "Lululemon", "year": "2024"}} (assuming this report is for FY2024, ending Jan 2025)
Example for 'Yonex 2025FY（2024.4-2025.3）.pdf': {{"company": "Yonex", "year": "2025"}}
Example for '361° 2024年報.pdf': {{"company": "361°", "year": "2024"}}
Example for 'ABC&D 2023.pdf': {{"company": "ABC&D", "year": "2023"}}

Ensure that special characters (e.g., '°', '&') that are part of a company's official name are retained in the 'company' field.
"""
        final_metadata_to_return: Dict[str, str]
        try:
            response_obj = self.llm.complete(prompt)
            response_str = response_obj.text # 获取文本内容
            # 尝试去除LLM可能返回的markdown代码块标记
            if response_str.startswith("```json\n") and response_str.endswith("\n```"):
                response_str = response_str[len("```json\n"):-len("\n```")]
            elif response_str.startswith("```") and response_str.endswith("```"):
                response_str = response_str[len("```"):-len("```")]
            
            metadata = json.loads(response_str)
            # 确保返回的是字典且包含期望的键
            if not isinstance(metadata, dict) or "company" not in metadata or "year" not in metadata:
                logger.warning(f"LLM为文件名 '{filename}' 返回的元数据格式不正确: {response_str}")
                final_metadata_to_return = {"company": "未知公司", "year": "未知年份"}
            else:
                # 基本的年份格式校验 (YYYY)
                year_str = str(metadata.get("year", "未知年份"))
                if not (year_str.isdigit() and len(year_str) == 4) and year_str != "未知年份":
                    logger.warning(f"LLM为文件名 '{filename}' 提取的年份格式不规范: '{year_str}'. 将使用 '未知年份'.")
                    metadata["year"] = "未知年份"

                current_extracted_metadata = {
                    "company": str(metadata.get("company", "未知公司")),
                    "year": year_str
                }
                final_metadata_to_return = current_extracted_metadata

        except json.JSONDecodeError:
            # response_str might not be defined if llm.complete() fails before assignment
            # However, the original code implies response_str would be available for logging.
            # For safety, check if response_str is defined or provide a default error message.
            err_response_str = response_str if 'response_str' in locals() else "[LLM response not available]"
            logger.error(f"LLM为文件名 '{filename}' 返回的内容无法解析为JSON: {err_response_str}")
            final_metadata_to_return = {"company": "未知公司", "year": "未知年份"}
        except Exception as e:
            logger.error(f"LLM从文件名 '{filename}' 提取元数据时发生错误: {e}")
            final_metadata_to_return = {"company": "未知公司", "year": "未知年份"}

        # Cache the result (success or structured failure) for the original filepath
        self.metadata_cache[original_filepath] = final_metadata_to_return
        logger.info(f"缓存并返回元数据 for '{original_filepath}' (from part '{filename}'): {final_metadata_to_return}")
        return final_metadata_to_return

    def load_financial_reports(self, reports_dir: str) -> List[Document]:
        """加载所有财报文档，使用自定义PDF处理（不切片）"""
        logger.info(f"开始加载财报目录: {reports_dir}")

        import os
        from llama_index.core import Document

        # 初始化改进的PDF提取器
        pdf_extractor = PDFTextExtractor()

        # 手动扫描目录中的文件
        documents = []
        supported_extensions = [".pdf", ".txt", ".docx", ".html", ".md"]

        for root, dirs, files in os.walk(reports_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                if file_ext in supported_extensions:
                    logger.info(f"发现文件: {file}")

                    try:
                        if file_ext == ".pdf":
                            # 使用我们的PDF提取器处理PDF文件
                            logger.info(f"  使用改进的PDF提取器处理: {file}")
                            print(f"处理PDF文件: {file}")

                            extraction_result = pdf_extractor.extract_text_best_effort(file_path)

                            if extraction_result['best_text']:
                                # 创建单个完整文档（不切片）
                                doc = Document(
                                    text=extraction_result['best_text'],
                                    doc_id=file_path,
                                    metadata={
                                        "file_name": file,
                                        "file_path": file_path,
                                        "file_type": "pdf",
                                        "pdf_extraction_method": extraction_result['best_method'],
                                        "pdf_extraction_score": extraction_result['best_score'],
                                        "doc_type": "financial_report"
                                    }
                                )

                                logger.info(f"  ✅ 使用 {extraction_result['best_method']} 成功提取，文本长度: {len(doc.text)}")
                                print(f"  ✅ 使用 {extraction_result['best_method']} 提取，文本长度: {len(doc.text)}")

                                # 显示文本预览
                                text_preview = doc.text[:300]
                                print(f"  📄 文本预览: {text_preview}")

                                documents.append(doc)
                            else:
                                logger.warning(f"  ❌ PDF提取器未能提取到文本: {file}")
                                print(f"  ❌ PDF提取器未能提取到文本: {file}")

                        else:
                            # 处理其他类型的文件（txt, docx, html, md）
                            logger.info(f"  处理非PDF文件: {file}")

                            # 使用SimpleDirectoryReader处理单个文件
                            reader = SimpleDirectoryReader(
                                input_files=[file_path],
                                filename_as_id=True
                            )

                            file_docs = reader.load_data()

                            for doc in file_docs:
                                doc.metadata["doc_type"] = "financial_report"
                                doc.metadata["file_type"] = file_ext[1:]  # 去掉点号
                                documents.append(doc)

                            logger.info(f"  ✅ 成功加载非PDF文件，获得 {len(file_docs)} 个文档片段")

                    except Exception as e:
                        logger.error(f"  ❌ 处理文件失败 {file}: {e}")
                        print(f"  ❌ 处理文件失败 {file}: {e}")
                        continue

        logger.info(f"扫描完成，共找到 {len(documents)} 个文档")
        print(f"扫描完成，共找到 {len(documents)} 个文档")

        # 为所有文档提取元数据
        for i, doc in enumerate(documents):
            filename = doc.metadata.get("file_name", "") or doc.doc_id or ""
            logger.info(f"提取元数据 {i+1}/{len(documents)}: {filename}")

            if filename:
                # 使用LLM提取元数据
                extracted_meta = self._extract_metadata_from_filename_llm(filename)
                doc.metadata["company"] = extracted_meta["company"]
                doc.metadata["year"] = extracted_meta["year"]
                logger.info(f"  📊 提取结果 -> 公司: {extracted_meta['company']}, 年份: {extracted_meta['year']}")
            else:
                doc.metadata["company"] = "未知公司"
                doc.metadata["year"] = "未知年份"
                logger.warning(f"  ⚠️  文档 {i+1} 无有效文件名，元数据设为未知")

        logger.info(f"🎉 成功加载并处理 {len(documents)} 份财报")
        return documents
    
    def build_index(self, documents: List[Document]) -> VectorStoreIndex:
        """构建向量索引"""
        logger.info("开始构建向量索引...")

        # 调试：在构建索引前检查文档文本
        logger.info("检查文档文本切片前的状态...")
        for i, doc in enumerate(documents[:3]):  # 只检查前3个文档
            if doc.text:
                text_preview = doc.text[:100]
                logger.info(f"文档 {i+1} 切片前文本预览: {text_preview}")
                print(f"文档 {i+1} 切片前文本预览: {text_preview}")
                logger.info(f"文档 {i+1} 总长度: {len(doc.text)} 字符")
                print(f"文档 {i+1} 总长度: {len(doc.text)} 字符")

        # 检查文档是否为空
        if not documents:
            logger.error("没有文档可以构建索引！")
            print("没有文档可以构建索引！")
            raise ValueError("没有文档可以构建索引")

        # 检查文档内容
        valid_docs = []
        for i, doc in enumerate(documents):
            if doc.text and len(doc.text.strip()) > 0:
                valid_docs.append(doc)
                logger.info(f"文档 {i+1} 有效，长度: {len(doc.text)}")
            else:
                logger.warning(f"文档 {i+1} 无效或为空")
                print(f"文档 {i+1} 无效或为空")

        if not valid_docs:
            logger.error("没有有效的文档可以构建索引！")
            print("没有有效的文档可以构建索引！")
            raise ValueError("没有有效的文档可以构建索引")

        logger.info(f"有效文档数量: {len(valid_docs)}")
        print(f"有效文档数量: {len(valid_docs)}")

        # 创建存储上下文
        storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )

        try:
            # 构建索引
            logger.info("开始构建向量索引...")
            print("开始构建向量索引...")

            index = VectorStoreIndex.from_documents(
                valid_docs,
                storage_context=storage_context,
                show_progress=True
            )

            logger.info("向量索引构建完成")
            print("向量索引构建完成")

        except Exception as e:
            logger.error(f"构建向量索引失败: {e}")
            print(f"构建向量索引失败: {e}")
            raise

        # 调试：检查索引中的节点文本
        logger.info("检查索引中的节点文本...")
        print("检查索引中的节点文本...")

        try:
            # 尝试多种方式获取节点
            logger.info("尝试获取索引节点...")

            # 方法1：通过docstore获取
            docstore_nodes = list(index.docstore.docs.values())
            logger.info(f"通过docstore获取到 {len(docstore_nodes)} 个节点")
            print(f"通过docstore获取到 {len(docstore_nodes)} 个节点")

            # 方法2：通过vector_store获取
            try:
                vector_store_nodes = self.vector_store.get_nodes()
                logger.info(f"通过vector_store获取到 {len(vector_store_nodes) if vector_store_nodes else 0} 个节点")
                print(f"通过vector_store获取到 {len(vector_store_nodes) if vector_store_nodes else 0} 个节点")
            except Exception as e:
                logger.warning(f"无法通过vector_store获取节点: {e}")

            # 方法3：通过index.index_struct获取
            try:
                if hasattr(index, 'index_struct') and hasattr(index.index_struct, 'nodes_dict'):
                    struct_nodes = list(index.index_struct.nodes_dict.values())
                    logger.info(f"通过index_struct获取到 {len(struct_nodes)} 个节点")
                    print(f"通过index_struct获取到 {len(struct_nodes)} 个节点")
            except Exception as e:
                logger.warning(f"无法通过index_struct获取节点: {e}")

            # 检查前几个节点的文本
            all_nodes = docstore_nodes
            if all_nodes:
                for i, node in enumerate(all_nodes[:5]):  # 只检查前5个节点
                    logger.info(f"检查节点 {i+1}...")
                    print(f"检查节点 {i+1}...")

                    # 尝试多种方式获取节点文本
                    node_text = None

                    if hasattr(node, 'text') and node.text:
                        node_text = node.text
                        logger.info(f"节点 {i+1} 通过text属性获取文本")
                    elif hasattr(node, 'get_content') and callable(node.get_content):
                        try:
                            node_text = node.get_content()
                            logger.info(f"节点 {i+1} 通过get_content()获取文本")
                        except Exception as e:
                            logger.warning(f"节点 {i+1} get_content()失败: {e}")
                    elif hasattr(node, 'content') and node.content:
                        node_text = node.content
                        logger.info(f"节点 {i+1} 通过content属性获取文本")

                    if node_text:
                        text_preview = node_text[:100]
                        logger.info(f"节点 {i+1} 文本预览: {text_preview}")
                        print(f"节点 {i+1} 文本预览: {text_preview}")
                        logger.info(f"节点 {i+1} 文本长度: {len(node_text)}")
                        print(f"节点 {i+1} 文本长度: {len(node_text)}")
                    else:
                        logger.warning(f"节点 {i+1} 没有可访问的文本内容")
                        print(f"节点 {i+1} 没有可访问的文本内容")

                        # 打印节点的所有属性
                        node_attrs = [attr for attr in dir(node) if not attr.startswith('_')]
                        logger.info(f"节点 {i+1} 可用属性: {node_attrs}")
            else:
                logger.warning("索引中没有找到任何节点！")
                print("索引中没有找到任何节点！")

        except Exception as e:
            logger.error(f"检查索引节点时出错: {e}")
            print(f"检查索引节点时出错: {e}")
            import traceback
            traceback.print_exc()

        return index

    def generate_evaluation_qa_dataset(
        self,
        index: VectorStoreIndex,
        num_questions: int = 100
    ) -> List[Dict[str, Any]]:
        """生成用于评估的高质量问答数据集，包含跨公司对比分析"""
        try:
            logger.info(f"开始生成评估数据集，目标问题数: {num_questions}")
            
            # 提取公司名称
            companies = self._extract_companies_from_index(index)
            if not companies:
                logger.warning("无法从索引中提取公司名称，请检查文档元数据")
                return []
            
            logger.info(f"检测到的公司: {', '.join(companies)}")
            
            # 生成单公司问题 (70%)
            single_company_questions = self._generate_single_company_questions(
                index=index,
                num_questions=int(num_questions * 0.7)
            )
            
            # 生成跨公司对比问题 (30%)
            cross_company_questions = self._generate_cross_company_questions(
                index=index,
                companies=companies,
                num_questions=int(num_questions * 0.3)
            )
            
            # 合并问题
            qa_pairs = single_company_questions + cross_company_questions
            
            # 添加问题类型和难度分类
            for qa_pair in qa_pairs:
                qa_pair["question_type"] = self._classify_question_type(qa_pair["question"])
                qa_pair["difficulty"] = self._assess_question_difficulty(qa_pair["question"])
                qa_pair["is_factual"] = self._is_factual_question(qa_pair["question"])
                qa_pair["requires_calculation"] = self._requires_calculation(qa_pair["question"])
            
            logger.info(f"生成了 {len(qa_pairs)} 个问答对")
            return qa_pairs
        except Exception as e:
            logger.error(f"生成问答数据集时出错: {e}")
            print(f"生成问答数据集时出错: {e}")
            return []

    def _extract_companies_from_index(self, index: VectorStoreIndex) -> List[str]:
        """从索引中提取公司名称"""
        companies = set()
        for doc in index.docstore.docs.values():
            company = doc.metadata.get("company", "")
            if company and company != "未知公司":
                companies.add(company)
        return list(companies)

    def _generate_single_company_questions(self, index: VectorStoreIndex, num_questions: int) -> List[Dict[str, Any]]:
        """生成单公司问题"""
        try:
            logger.info(f"生成 {num_questions} 个单公司问题...")
            print(f"开始生成 {num_questions} 个单公司问题...")
            
            # 获取所有文档节点
            documents = list(index.docstore.docs.values())
            print(f"获取到 {len(documents)} 个文档节点")
            
            # 创建问题生成器
            from llama_index.question_gen import QuestionGenerator
            question_generator = QuestionGenerator.from_defaults()
        
        except Exception as e:
            logger.error(f"创建问题生成器失败: {e}")
            return []
        
        logger.info("创建问题生成器成功，开始生成问题...")
        # 生成问题
        questions = []
        # The 'Document' class should already be imported from llama_index.core
        for i, doc in enumerate(documents):
            if len(questions) >= num_questions: # Check total questions generated
                break
            try:
                company_name = doc.metadata.get("company", "")
                company_name_for_prompt = company_name if company_name and company_name != "未知公司" else "the company"

                # Heuristic to identify sporting goods companies - can be expanded
                sporting_keywords = ["sport", "athletic", "apparel", "footwear", "equipment", "体育", "运动", "361°", "耐克", "阿迪达斯", "李宁", "安踏"]
                is_sporting_company = any(keyword.lower() in company_name_for_prompt.lower() for keyword in sporting_keywords)

                company_specific_instruction = f"The report is for {company_name_for_prompt}. "
                if is_sporting_company:
                    company_specific_instruction += (
                        f"Since '{company_name_for_prompt}' appears to be a sporting goods company, "
                        "please generate questions that are particularly relevant to a company in the sporting goods sector. "
                        "Consider aspects like its market share in specific sports categories, product innovation, athlete endorsements, "
                        "sponsorships, supply chain challenges for sporting goods, sustainability in sportswear, e-commerce strategy for sports products, "
                        "or its competitive performance against other major sports brands. "
                    )
                else:
                    company_specific_instruction += (
                        "Please generate general insightful questions about its financial health, overall strategy, market position, risks, and future outlook."
                    )

                context_for_generation = (
                    f"Instructions for question generation:\n"
                    f"{company_specific_instruction}\n\n"
                    f"Based on the following document text for {company_name_for_prompt}, please generate exactly 1 insightful question suitable for evaluating an AI's understanding of this financial report:\n"
                    f"---------------------\n"
                    f"{doc.text}\n"
                    f"---------------------\n"
                )
                
                # Create a new Document object with the modified text for question generation
                temp_doc_for_qgen = Document(text=context_for_generation, metadata=doc.metadata.copy())

                doc_questions = question_generator.generate(
                    [temp_doc_for_qgen], # Pass as a list of Documents
                    num_questions=1 # Generate 1 question based on the provided context
                )
                questions.extend(doc_questions)
                if len(questions) % 5 == 0:
                    print(f"已生成 {len(questions)} 个问题")
            except Exception as e:
                print(f"生成文档 {i} 问题时出错: {e}")
                logger.error(f"生成文档 {i} 问题时出错: {e}")
            
            # 确保问题数量不超过要求
            questions = questions[:num_questions]
            print(f"成功生成 {len(questions)} 个问题")
            
            # 创建查询引擎
            query_engine = index.as_query_engine(
                similarity_top_k=5,
                response_mode="tree_summarize"
            )
            
            # 为每个问题生成答案
            qa_pairs = []
            for i, question in enumerate(questions):
                try:
                    print(f"生成问题 {i+1}/{len(questions)} 的答案: {question[:50]}...")
                    logger.info(f"生成单公司答案 {i+1}/{len(questions)}: {question[:50]}...")
                    
                    # 使用查询引擎生成答案
                    response = query_engine.query(question)
                    
                    # 提取源节点信息
                    source_info = []
                    if hasattr(response, 'source_nodes') and response.source_nodes:
                        for node in response.source_nodes:
                            source_info.append({
                                "company": node.node.metadata.get("company", "unknown"),
                                "year": node.node.metadata.get("year", "unknown"),
                                "score": getattr(node, 'score', 0.0)
                            })

                    qa_pair = {
                        "question": question,
                        "answer": str(response),
                        "question_type": self._classify_question_type(question),
                        "difficulty": self._assess_question_difficulty(question),
                        "source_documents": source_info,
                        "generated_at": datetime.now().isoformat(),
                        "is_cross_company": False,  # 标记为单公司问题
                        "evaluation_metadata": {
                            "is_factual": self._is_factual_question(question),
                            "requires_calculation": self._requires_calculation(question),
                            "answer_length": len(str(response)),
                            "confidence_score": getattr(response, 'confidence', 0.0)
                        }
                    }

                    qa_pairs.append(qa_pair)

                except Exception as e:
                    logger.error(f"生成单公司问答对失败: {e}")
                    print(f"生成单公司问答对失败: {e}")
                    continue

            return qa_pairs

    def _generate_cross_company_questions(self, index: VectorStoreIndex, companies: List[str], num_questions: int) -> List[Dict[str, Any]]:
        """生成跨公司对比问题"""
        qa_pairs = []
        try:
            logger.info(f"生成 {num_questions} 个跨公司对比问题...")
            print(f"开始生成 {num_questions} 个跨公司对比问题...")
            
            if len(companies) < 2:
                logger.warning("公司数量不足，无法生成跨公司问题")
                print("公司数量不足，无法生成跨公司问题")
                return []
            
            # 创建查询引擎
            query_engine = index.as_query_engine(
                similarity_top_k=10,  # 增加检索数量以获取多公司信息
                response_mode="tree_summarize"
            )
            
            # 定义跨公司对比问题模板
            comparison_templates = [
                # 营收对比
                "比较{company1}和{company2}的营业收入情况，哪家公司表现更好？",
                "分析{company1}、{company2}和{company3}的营收增长率，排名如何？",
                
                # 盈利能力对比
                "对比{company1}和{company2}的净利润率，分析差异原因",
                "比较{company1}和{company2}的毛利率水平，哪家更有竞争优势？",
                
                # 财务健康度对比
                "分析{company1}和{company2}的资产负债率，哪家财务更稳健？",
                "比较{company1}和{company2}的现金流状况，评估其流动性",
                
                # 成长性对比
                "对比{company1}和{company2}的业务增长潜力和发展前景",
                "分析{company1}和{company2}的研发投入占比，哪家更注重创新？",
                
                # 行业地位对比
                "比较{company1}和{company2}在行业中的市场地位和竞争优势",
                "分析{company1}、{company2}和{company3}的市场份额变化趋势"
            ]
            
            import random
            import itertools
            
            # 生成公司组合
            company_pairs = list(itertools.combinations(companies, 2))
            company_triplets = list(itertools.combinations(companies, 3)) if len(companies) >= 3 else []
            
            print(f"生成了 {len(company_pairs)} 个公司对和 {len(company_triplets)} 个公司三元组")
            
            questions_generated = 0
            
            while questions_generated < num_questions and (company_pairs or company_triplets):
                try:
                    # 随机选择模板
                    template = random.choice(comparison_templates)
                    
                    # 根据模板需要的公司数量选择公司组合
                    if "{company3}" in template and company_triplets:
                        company_combo = random.choice(company_triplets)
                        question = template.format(
                            company1=company_combo[0],
                            company2=company_combo[1],
                            company3=company_combo[2]
                        )
                        involved_companies = list(company_combo)
                    elif company_pairs:
                        company_combo = random.choice(company_pairs)
                        question = template.format(
                            company1=company_combo[0],
                            company2=company_combo[1]
                        )
                        involved_companies = list(company_combo)
                    else:
                        break
                    
                    print(f"生成跨公司问题 {questions_generated+1}/{num_questions}: {question[:50]}...")
                    logger.info(f"生成跨公司问题 {questions_generated+1}/{num_questions}: {question[:50]}...")
                    
                    # 使用查询引擎生成答案
                    response = query_engine.query(question)
                    
                    # 提取源节点信息，确保包含多个公司的信息
                    source_info = []
                    companies_in_sources = set()
                    
                    if hasattr(response, 'source_nodes') and response.source_nodes:
                        for node in response.source_nodes:
                            company = node.node.metadata.get("company", "unknown")
                            companies_in_sources.add(company)
                            source_info.append({
                                "company": company,
                                "year": node.node.metadata.get("year", "unknown"),
                                "score": getattr(node, 'score', 0.0)
                            })
                    
                    # 检查是否真的包含了多个公司的信息
                    if len(companies_in_sources) < 2:
                        logger.warning(f"跨公司问题的答案只包含 {len(companies_in_sources)} 个公司的信息，跳过")
                        print(f"跨公司问题的答案只包含 {len(companies_in_sources)} 个公司的信息，跳过")
                        continue
                except Exception as e:
                    logger.error(f"生成单个跨公司问题时出错: {e}")
                    print(f"生成单个跨公司问题时出错: {e}")
                    continue

                qa_pair = {
                    "question": question,
                    "answer": str(response),
                    "question_type": "比较型",  # 跨公司问题都是比较型
                    "difficulty": "中等",  # 跨公司对比通常是中等难度
                    "source_documents": source_info,
                    "generated_at": datetime.now().isoformat(),
                    "is_cross_company": True,  # 标记为跨公司问题
                    "involved_companies": involved_companies,  # 记录涉及的公司
                    "evaluation_metadata": {
                        "is_factual": self._is_factual_question(question),
                        "requires_calculation": self._requires_calculation(question),
                        "answer_length": len(str(response)),
                        "confidence_score": getattr(response, 'confidence', 0.0),
                        "companies_in_answer": len(companies_in_sources)
                    }
                }

                qa_pairs.append(qa_pair)
                questions_generated += 1

        except Exception as e:
            logger.error(f"生成跨公司问答对失败: {e}")
            print(f"生成跨公司问答对失败: {e}")
            return qa_pairs

        logger.info(f"成功生成 {len(qa_pairs)} 个跨公司对比问答对")
        return qa_pairs

    def _classify_question_type(self, question: str) -> str:
        """分类问题类型"""
        question_lower = question.lower()

        if any(word in question_lower for word in ["多少", "数量", "金额", "比例", "率"]):
            return "数值型"
        elif any(word in question_lower for word in ["为什么", "原因", "如何", "怎样"]):
            return "解释型"
        elif any(word in question_lower for word in ["比较", "对比", "差异", "区别"]):
            return "比较型"
        elif any(word in question_lower for word in ["趋势", "变化", "增长", "下降"]):
            return "趋势型"
        elif any(word in question_lower for word in ["风险", "挑战", "机遇", "前景"]):
            return "分析型"
        else:
            return "一般型"

    def _assess_question_difficulty(self, question: str) -> str:
        """评估问题难度"""
        # 简单的难度评估逻辑
        complexity_indicators = [
            "综合", "分析", "评估", "预测", "对比", "影响", "策略", "风险"
        ]

        complexity_count = sum(1 for indicator in complexity_indicators
                             if indicator in question)

        if complexity_count >= 3:
            return "困难"
        elif complexity_count >= 1:
            return "中等"
        else:
            return "简单"

    def _is_factual_question(self, question: str) -> bool:
        """判断是否为事实性问题"""
        factual_indicators = [
            "多少", "什么", "哪个", "何时", "数量", "金额", "比例"
        ]
        return any(indicator in question for indicator in factual_indicators)

    def _requires_calculation(self, question: str) -> bool:
        """判断是否需要计算"""
        calculation_indicators = [
            "增长率", "比例", "百分比", "倍数", "差额", "总计", "平均"
        ]
        return any(indicator in question for indicator in calculation_indicators)

    def save_evaluation_dataset(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """保存评估数据集"""
        logger.info(f"保存评估数据集到: {output_file}")

        # 保存为 JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qa_pairs, f, ensure_ascii=False, indent=2)

        # 生成统计报告
        self._generate_dataset_report(qa_pairs, output_file)

        # 保存为 Excel 便于查看
        self._save_as_excel(qa_pairs, output_file)

        logger.info(f"评估数据集已保存到 {output_file}")

    def _generate_dataset_report(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """生成数据集统计报告"""
        report = {
            "总问题数": len(qa_pairs),
            "问题类型分布": {},
            "难度分布": {},
            "事实性问题比例": 0,
            "需要计算的问题比例": 0,
            "平均答案长度": 0,
            "跨公司分析": {
                "跨公司问题数": 0,
                "单公司问题数": 0,
                "跨公司问题比例": 0,
                "涉及的公司": set(),
                "公司组合分析": {}
            },
            "生成时间": datetime.now().isoformat()
        }

        # 统计问题类型和跨公司分析
        for qa in qa_pairs:
            q_type = qa.get("question_type", "未知")
            report["问题类型分布"][q_type] = report["问题类型分布"].get(q_type, 0) + 1

            difficulty = qa.get("difficulty", "未知")
            report["难度分布"][difficulty] = report["难度分布"].get(difficulty, 0) + 1

            # 跨公司分析统计
            is_cross_company = qa.get("is_cross_company", False)
            if is_cross_company:
                report["跨公司分析"]["跨公司问题数"] += 1
                # 记录涉及的公司
                involved_companies = qa.get("involved_companies", [])
                for company in involved_companies:
                    report["跨公司分析"]["涉及的公司"].add(company)

                # 统计公司组合
                if len(involved_companies) >= 2:
                    combo_key = " vs ".join(sorted(involved_companies))
                    report["跨公司分析"]["公司组合分析"][combo_key] = \
                        report["跨公司分析"]["公司组合分析"].get(combo_key, 0) + 1
            else:
                report["跨公司分析"]["单公司问题数"] += 1

        # 统计其他指标
        factual_count = sum(1 for qa in qa_pairs
                           if qa.get("evaluation_metadata", {}).get("is_factual", False))
        calc_count = sum(1 for qa in qa_pairs
                        if qa.get("evaluation_metadata", {}).get("requires_calculation", False))

        report["事实性问题比例"] = factual_count / len(qa_pairs) if qa_pairs else 0
        report["需要计算的问题比例"] = calc_count / len(qa_pairs) if qa_pairs else 0

        # 平均答案长度
        total_length = sum(qa.get("evaluation_metadata", {}).get("answer_length", 0)
                          for qa in qa_pairs)
        report["平均答案长度"] = total_length / len(qa_pairs) if qa_pairs else 0

        # 完善跨公司分析统计
        cross_company_count = report["跨公司分析"]["跨公司问题数"]
        report["跨公司分析"]["跨公司问题比例"] = cross_company_count / len(qa_pairs) if qa_pairs else 0

        # 将set转换为list以便JSON序列化
        report["跨公司分析"]["涉及的公司"] = list(report["跨公司分析"]["涉及的公司"])

        # 保存报告
        report_file = output_file.replace('.json', '_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"数据集报告已保存到 {report_file}")

        # 打印关键统计信息
        logger.info(f"数据集统计摘要:")
        logger.info(f"  总问题数: {len(qa_pairs)}")
        logger.info(f"  跨公司问题: {cross_company_count} ({report['跨公司分析']['跨公司问题比例']:.1%})")
        logger.info(f"  单公司问题: {report['跨公司分析']['单公司问题数']}")
        logger.info(f"  涉及公司: {report['跨公司分析']['涉及的公司']}")
        if report["跨公司分析"]["公司组合分析"]:
            logger.info(f"  主要公司组合: {list(report['跨公司分析']['公司组合分析'].keys())[:3]}")

    def _save_as_excel(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """保存为Excel格式"""
        df_data = []
        for qa in qa_pairs:
            # 处理涉及的公司信息
            involved_companies = qa.get("involved_companies", [])
            companies_str = ", ".join(involved_companies) if involved_companies else ""

            df_data.append({
                "问题": qa["question"],
                "答案": qa["answer"],
                "问题类型": qa.get("question_type", ""),
                "难度": qa.get("difficulty", ""),
                "是否跨公司": qa.get("is_cross_company", False),
                "涉及公司": companies_str,
                "是否事实性": qa.get("evaluation_metadata", {}).get("is_factual", False),
                "需要计算": qa.get("evaluation_metadata", {}).get("requires_calculation", False),
                "答案长度": qa.get("evaluation_metadata", {}).get("answer_length", 0),
                "答案中公司数": qa.get("evaluation_metadata", {}).get("companies_in_answer", 1),
                "置信度": qa.get("evaluation_metadata", {}).get("confidence_score", 0),
                "生成时间": qa.get("generated_at", "")
            })

        df = pd.DataFrame(df_data)
        excel_file = output_file.replace('.json', '.xlsx')
        df.to_excel(excel_file, index=False)

        logger.info(f"Excel文件已保存到 {excel_file}")

    def run_evaluation_dataset_generation(
        self,
        reports_dir: str,
        output_file: str,
        num_questions: int = 100
    ) -> List[Dict[str, Any]]:
        """运行完整的评估数据集生成流程"""
        logger.info("="*60)
        logger.info("开始生成财报问答评估数据集")
        logger.info("="*60)

        try:
            # 1. 加载财报
            documents = self.load_financial_reports(reports_dir)
            if not documents:
                logger.error("没有找到任何财报文档")
                return []

            # 2. 构建索引
            index = self.build_index(documents)

            # 3. 生成评估问答对
            qa_pairs = self.generate_evaluation_qa_dataset(index, num_questions)

            # 4. 保存评估数据集
            if qa_pairs:
                self.save_evaluation_dataset(qa_pairs, output_file)
            else:
                logger.warning("没有生成任何问答对")

            logger.info("="*60)
            logger.info("评估数据集生成完成！")
            logger.info(f"共生成 {len(qa_pairs)} 个高质量问答对")
            logger.info("="*60)

            return qa_pairs

        except Exception as e:
            logger.error(f"生成过程中出现错误: {e}")
            return []


# 使用命令行参数
import argparse

if __name__ == "__main__":
    try:
        # 设置命令行参数
        parser = argparse.ArgumentParser(description="财报问答评估数据集生成器")
        parser.add_argument(
            "--reports_dir", 
            type=str, 
            default="./reports", 
            help="财报文件夹路径"
        )
        parser.add_argument(
            "--output_file", 
            type=str, 
            default="./evaluation_qa_dataset.json", 
            help="输出文件路径"
        )
        parser.add_argument(
            "--num_questions", 
            type=int, 
            default=10, 
            help="生成问题数量"
        )
        args = parser.parse_args()
        
        print("开始执行程序...")
        print(f"报告目录: {args.reports_dir}")
        print(f"输出文件: {args.output_file}")
        print(f"问题数量: {args.num_questions}")
        
        # 检查报告目录是否存在
        import os
        if not os.path.exists(args.reports_dir):
            print(f"错误: 报告目录 '{args.reports_dir}' 不存在")
            raise FileNotFoundError(f"报告目录 '{args.reports_dir}' 不存在")
        
        # 检查报告目录中是否有文件
        files = os.listdir(args.reports_dir)
        print(f"报告目录中的文件数量: {len(files)}")
        if len(files) == 0:
            print(f"警告: 报告目录 '{args.reports_dir}' 中没有文件")
        
        print("验证配置...")
        # 验证配置
        from config import Config
        config = Config()
        if not config.validate_config():
            print("配置验证失败，请检查环境变量")
            raise ValueError("配置验证失败，请检查环境变量")
        else:
            print("配置验证成功")
        
        print("创建生成器实例...")
        # 创建生成器实例
        generator = FinancialQAGenerator()

        # 运行评估数据集生成流程
        qa_pairs = generator.run_evaluation_dataset_generation(
            reports_dir=args.reports_dir,  # 财报文件夹
            output_file=args.output_file,  # 输出文件
            num_questions=args.num_questions # 生成问题数量
        )

        # 打印示例结果和统计信息
        if qa_pairs:
            print("\n=== 生成的评估问答对示例 ===")

            # 显示单公司问题示例
            single_company_qa = [qa for qa in qa_pairs if not qa.get('is_cross_company', False)]
            if single_company_qa:
                print("\n【单公司问题示例】")
                qa = single_company_qa[0]
                print(f"问题: {qa['question']}")
                print(f"答案: {qa['answer'][:200]}...")
                print(f"类型: {qa['question_type']} | 难度: {qa['difficulty']}")
                print("-" * 80)

            # 显示跨公司对比问题示例
            cross_company_qa = [qa for qa in qa_pairs if qa.get('is_cross_company', False)]
            if cross_company_qa:
                print("\n【跨公司对比问题示例】")
                for i, qa in enumerate(cross_company_qa[:2]):
                    print(f"\n跨公司问题 {i+1}: {qa['question']}")
                    print(f"答案: {qa['answer'][:200]}...")
                    print(f"涉及公司: {qa.get('involved_companies', [])}")
                    print(f"答案中公司数: {qa['evaluation_metadata'].get('companies_in_answer', 0)}")
                    print("-" * 80)

            # 统计信息
            print(f"\n=== 数据集统计 ===")
            print(f"总问题数: {len(qa_pairs)}")
            print(f"单公司问题: {len(single_company_qa)}")
            print(f"跨公司问题: {len(cross_company_qa)}")

            type_counts = {}
            difficulty_counts = {}
            companies_involved = set()

            for qa in qa_pairs:
                q_type = qa.get('question_type', '未知')
                difficulty = qa.get('difficulty', '未知')
                type_counts[q_type] = type_counts.get(q_type, 0) + 1
                difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1

                # 收集涉及的公司
                if qa.get('involved_companies'):
                    companies_involved.update(qa['involved_companies'])

            print("问题类型分布:", type_counts)
            print("难度分布:", difficulty_counts)
            print("涉及的公司:", list(companies_involved))

            if cross_company_qa:
                print(f"跨公司问题比例: {len(cross_company_qa)/len(qa_pairs):.1%}")

                # 显示公司组合统计
                combo_counts = {}
                for qa in cross_company_qa:
                    companies = qa.get('involved_companies', [])
                    if len(companies) >= 2:
                        combo_key = " vs ".join(sorted(companies))
                        combo_counts[combo_key] = combo_counts.get(combo_key, 0) + 1

                if combo_counts:
                    print("公司组合分布:", dict(list(combo_counts.items())[:5]))

        else:
            print("没有生成任何问答对，请检查配置和数据")

    except Exception as e:
        logger.error(f"程序运行失败:")
        logger.error(traceback.format_exc())
        print(f"错误详情:")
        traceback.print_exc()
        print("请检查:")
        print("1. 环境变量配置是否正确")
        print("2. API密钥是否有效")
        print("3. 财报文件夹是否存在")
        print("4. 依赖包是否正确安装")
