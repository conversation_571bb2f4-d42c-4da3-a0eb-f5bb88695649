#!/usr/bin/env python3
"""
测试SimpleDirectoryReader是否能解决PDF乱码问题
"""

import os
from llama_index.core import SimpleDirectoryReader

def test_simple_reader():
    """测试SimpleDirectoryReader处理PDF的效果"""
    
    # 检查PDF文件
    reports_dir = "./reports"
    pdf_files = [f for f in os.listdir(reports_dir) if f.endswith('.pdf')]
    
    if not pdf_files:
        print("没有找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件:")
    for pdf in pdf_files:
        print(f"  - {pdf}")
    
    # 测试两个PDF文件
    for pdf_idx, first_pdf in enumerate(pdf_files):
        pdf_path = os.path.join(reports_dir, first_pdf)

        print(f"\n测试文件 {pdf_idx + 1}: {first_pdf}")
        print("=" * 50)

        try:
            # 使用SimpleDirectoryReader
            reader = SimpleDirectoryReader(
                input_files=[pdf_path],
                filename_as_id=True
            )

            documents = reader.load_data()
            print(f"SimpleDirectoryReader加载了 {len(documents)} 个文档片段")

            # 检查前几个文档片段的文本
            for i, doc in enumerate(documents[:3]):  # 只检查前3个
                print(f"\n--- 文档片段 {i+1} ---")
                print(f"文档ID: {doc.doc_id}")
                print(f"文本长度: {len(doc.text) if doc.text else 0}")

                if doc.text:
                    # 显示前200个字符
                    preview = doc.text[:200]
                    print(f"文本预览: {preview}")

                    # 检查是否有乱码特征
                    garbled_chars = ['º', '׵', 'މ', '೐', 'ʮ', 'ɚ', 'ཧ', 'ϋ', 'జ']
                    has_garbled = any(char in preview for char in garbled_chars)
                    print(f"是否包含乱码字符: {has_garbled}")

                    if has_garbled:
                        print("❌ SimpleDirectoryReader无法解决乱码问题")
                    else:
                        print("✅ 文本看起来正常")
                else:
                    print("⚠️  文档没有文本内容")

        except Exception as e:
            print(f"❌ SimpleDirectoryReader处理失败: {e}")

        print("\n" + "="*50)

if __name__ == "__main__":
    test_simple_reader()
