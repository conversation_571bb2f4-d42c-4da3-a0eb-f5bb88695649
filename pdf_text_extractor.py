#!/usr/bin/env python3
"""
改进的PDF文本提取器
支持多种PDF解析库，处理繁体中文乱码问题
"""

import os
import logging
from typing import List, Optional, Dict, Any
import re

logger = logging.getLogger(__name__)

class PDFTextExtractor:
    """改进的PDF文本提取器"""
    
    def __init__(self):
        self.available_extractors = []
        self._check_available_extractors()
    
    def _check_available_extractors(self):
        """检查可用的PDF提取器"""
        # 检查pypdf
        try:
            import pypdf
            self.available_extractors.append('pypdf')
            logger.info("pypdf 可用")
        except ImportError:
            logger.warning("pypdf 不可用")
        
        # 检查pdfplumber
        try:
            import pdfplumber
            self.available_extractors.append('pdfplumber')
            logger.info("pdfplumber 可用")
        except ImportError:
            logger.warning("pdfplumber 不可用")
        
        # 检查pymupdf (fitz)
        try:
            import fitz
            self.available_extractors.append('pymupdf')
            logger.info("pymupdf 可用")
        except ImportError:
            logger.warning("pymupdf 不可用")
        
        # 检查pdfminer
        try:
            from pdfminer.high_level import extract_text
            self.available_extractors.append('pdfminer')
            logger.info("pdfminer 可用")
        except ImportError:
            logger.warning("pdfminer 不可用")
    
    def extract_text_pypdf(self, pdf_path: str) -> str:
        """使用pypdf提取文本"""
        try:
            import pypdf
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = pypdf.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            logger.error(f"pypdf提取失败: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """使用pdfplumber提取文本"""
        try:
            import pdfplumber
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")
            return ""
    
    def extract_text_pymupdf(self, pdf_path: str) -> str:
        """使用pymupdf提取文本"""
        try:
            import fitz
            text = ""
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_text = page.get_text()
                if page_text:
                    text += page_text + "\n"
            doc.close()
            return text
        except Exception as e:
            logger.error(f"pymupdf提取失败: {e}")
            return ""
    
    def extract_text_pdfminer(self, pdf_path: str) -> str:
        """使用pdfminer提取文本"""
        try:
            from pdfminer.high_level import extract_text
            text = extract_text(pdf_path)
            return text if text else ""
        except Exception as e:
            logger.error(f"pdfminer提取失败: {e}")
            return ""
    
    def is_text_garbled(self, text: str) -> bool:
        """检测文本是否乱码"""
        if not text:
            return True
        
        # 检查乱码指标
        garbled_indicators = ['�', '\ufffd', '?']
        has_garbled_chars = any(indicator in text for indicator in garbled_indicators)
        
        # 检查中文字符比例
        chinese_chars = len([char for char in text if '\u4e00' <= char <= '\u9fff'])
        total_chars = len([char for char in text if char.isalnum() or '\u4e00' <= char <= '\u9fff'])
        
        if total_chars == 0:
            return True
        
        chinese_ratio = chinese_chars / total_chars
        
        # 如果有明显乱码字符，或者中文字符比例异常低（对于中文PDF），则认为是乱码
        if has_garbled_chars:
            return True
        
        # 对于应该包含中文的文档，如果中文字符比例太低，可能是乱码
        if chinese_ratio < 0.1 and total_chars > 100:
            # 检查是否有大量奇怪的字符组合
            weird_char_count = len(re.findall(r'[^\u4e00-\u9fff\w\s\.,;:!?()[\]{}"\'-]', text))
            if weird_char_count > total_chars * 0.3:
                return True
        
        return False

    def clean_and_fix_text(self, text: str) -> str:
        """清理和修复文本中的编码问题"""
        if not text:
            return text

        # 移除明显的乱码字符
        cleaned_text = text

        # 替换常见的乱码字符和特殊符号
        replacements = {
            '�': '',  # 替换乱码字符
            '\ufffd': '',  # 替换替换字符
            '׵': '',  # 移除特定乱码字符
            'މ': '',  # 移除特定乱码字符
            'ʮ̡': '',  # 移除特定乱码组合
            'ʿ': '',  # 移除特定乱码字符
            '̨': '',  # 移除特定乱码字符
            'ɛ': '',  # 移除特定乱码字符
            '͏': '',  # 移除特定乱码字符
            '࿆': '',  # 移除特定乱码字符
            'ᓞ': '',  # 移除特定乱码字符
            '΅': '',  # 移除特定乱码字符
            '˾': '',  # 移除特定乱码字符
            '໮': '',  # 移除特定乱码字符
            'ಥ': '',  # 移除特定乱码字符
        }

        for old, new in replacements.items():
            cleaned_text = cleaned_text.replace(old, new)

        # 移除包含大量乱码的行
        lines = cleaned_text.split('\n')
        clean_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查这一行是否主要是乱码
            if self.is_line_mostly_garbled(line):
                logger.debug(f"移除乱码行: {line[:50]}...")
                continue

            clean_lines.append(line)

        cleaned_text = '\n'.join(clean_lines)

        # 移除连续的空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

        # 移除行首行尾空白
        cleaned_text = cleaned_text.strip()

        return cleaned_text

    def is_line_mostly_garbled(self, line: str) -> bool:
        """检查一行文本是否主要是乱码"""
        if len(line) < 3:
            return False

        # 计算各种字符的比例
        total_chars = len(line)
        chinese_chars = len([c for c in line if '\u4e00' <= c <= '\u9fff'])
        ascii_chars = len([c for c in line if c.isascii() and c.isalnum()])
        weird_chars = len([c for c in line if not (c.isspace() or c.isascii() or '\u4e00' <= c <= '\u9fff')])

        # 如果奇怪字符占比超过50%，认为是乱码
        if weird_chars / total_chars > 0.5:
            return True

        # 如果没有中文字符，且奇怪字符占比超过30%，认为是乱码
        if chinese_chars == 0 and weird_chars / total_chars > 0.3:
            return True

        return False

    def extract_text_best_effort(self, pdf_path: str) -> Dict[str, Any]:
        """使用最佳方法提取文本"""
        results = {}
        best_text = ""
        best_method = ""
        best_score = -1

        # 尝试所有可用的提取器
        for extractor in self.available_extractors:
            try:
                if extractor == 'pypdf':
                    text = self.extract_text_pypdf(pdf_path)
                elif extractor == 'pdfplumber':
                    text = self.extract_text_pdfplumber(pdf_path)
                elif extractor == 'pymupdf':
                    text = self.extract_text_pymupdf(pdf_path)
                elif extractor == 'pdfminer':
                    text = self.extract_text_pdfminer(pdf_path)
                else:
                    continue

                # 清理和修复文本
                cleaned_text = self.clean_and_fix_text(text)

                # 评估文本质量
                score = self.evaluate_text_quality(cleaned_text)
                results[extractor] = {
                    'text': cleaned_text,
                    'score': score,
                    'length': len(cleaned_text),
                    'is_garbled': self.is_text_garbled(cleaned_text)
                }

                logger.info(f"{extractor}: 长度={len(cleaned_text)}, 分数={score:.2f}, 乱码={results[extractor]['is_garbled']}")

                if score > best_score:
                    best_score = score
                    best_text = cleaned_text
                    best_method = extractor

            except Exception as e:
                logger.error(f"{extractor} 提取失败: {e}")
                results[extractor] = {
                    'text': "",
                    'score': -1,
                    'length': 0,
                    'is_garbled': True,
                    'error': str(e)
                }

        return {
            'best_text': best_text,
            'best_method': best_method,
            'best_score': best_score,
            'all_results': results
        }
    
    def evaluate_text_quality(self, text: str) -> float:
        """评估文本质量"""
        if not text:
            return 0.0
        
        score = 0.0
        
        # 基础分数：有文本内容
        score += 1.0
        
        # 长度分数
        if len(text) > 100:
            score += 1.0
        if len(text) > 1000:
            score += 1.0
        
        # 中文字符分数
        chinese_chars = len([char for char in text if '\u4e00' <= char <= '\u9fff'])
        if chinese_chars > 0:
            score += 2.0
        if chinese_chars > 50:
            score += 1.0
        
        # 乱码惩罚
        if self.is_text_garbled(text):
            score -= 3.0
        
        # 结构化内容分数
        if '公司' in text or '報告' in text or '年報' in text:
            score += 1.0
        
        # 数字和标点符号分数
        if re.search(r'\d+', text):
            score += 0.5
        if re.search(r'[，。；：！？]', text):
            score += 0.5
        
        return max(0.0, score)

def test_pdf_extractor():
    """测试PDF提取器"""
    extractor = PDFTextExtractor()
    
    # 测试文件
    test_files = [
        "./reports/361° 2024年報.pdf",
        "./reports/安踏 2024 年年报.pdf"
    ]
    
    for pdf_file in test_files:
        if os.path.exists(pdf_file):
            print(f"\n=== 测试文件: {pdf_file} ===")
            result = extractor.extract_text_best_effort(pdf_file)
            
            print(f"最佳方法: {result['best_method']}")
            print(f"最佳分数: {result['best_score']:.2f}")
            print(f"文本长度: {len(result['best_text'])}")
            
            if result['best_text']:
                preview = result['best_text'][:200]
                print(f"文本预览: {preview}")
                
                # 检查中文字符
                chinese_chars = [char for char in preview if '\u4e00' <= char <= '\u9fff']
                print(f"中文字符数量: {len(chinese_chars)}")
                if chinese_chars:
                    print(f"中文字符示例: {''.join(chinese_chars[:10])}")
            
            # 显示所有方法的结果
            print("\n所有方法结果:")
            for method, data in result['all_results'].items():
                print(f"  {method}: 长度={data['length']}, 分数={data['score']:.2f}, 乱码={data['is_garbled']}")

if __name__ == "__main__":
    test_pdf_extractor()
